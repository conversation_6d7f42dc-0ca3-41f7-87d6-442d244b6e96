type TranslationFunction = (key: string) => string;

// Function to get products with translations - Main products only
export const getProducts = (t: TranslationFunction) => [
  {
    slug: "ai-annotation",
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    iconName: "Brain",
    features: [
      { text: t("products.aiAnnotation.features.imageAnnotation"), iconName: "Eye" },
      { text: t("products.aiAnnotation.features.textAnnotation"), iconName: "Database" },
      { text: t("products.aiAnnotation.features.audioAnnotation"), iconName: "Target" },
      { text: t("products.aiAnnotation.features.qualityControl"), iconName: "CheckCircle2" }
    ],
    highlight: t("products.aiAnnotation.highlight"),
    price: t("products.aiAnnotation.price"),
    category: t("products.aiAnnotation.category")
  },
  {
    slug: "cpu-rental",
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    iconName: "Cpu",
    features: [
      { text: t("products.cpuRental.features.highPerformance"), iconName: "Zap" },
      { text: t("products.cpuRental.features.elasticScaling"), iconName: "Globe" },
      { text: t("products.cpuRental.features.payAsYouGo"), iconName: "BarChart" },
      { text: t("products.cpuRental.features.monitoring247"), iconName: "Shield" }
    ],
    highlight: t("products.cpuRental.highlight"),
    price: t("products.cpuRental.price"),
    category: t("products.cpuRental.category")
  },
  {
    slug: "education-management",
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    iconName: "GraduationCap",
    features: [
      { text: t("products.educationManagement.features.courseManagement"), iconName: "BookOpen" },
      { text: t("products.educationManagement.features.onlineExam"), iconName: "Target" },
      { text: t("products.educationManagement.features.studentTracking"), iconName: "Users" },
      { text: t("products.educationManagement.features.certificateSystem"), iconName: "Award" }
    ],
    highlight: t("products.educationManagement.highlight"),
    price: t("products.educationManagement.price"),
    category: t("products.educationManagement.category")
  }
];

// Function to get product details with translations
export const getProductDetails = (t: TranslationFunction) => ({
  "ai-annotation": {
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    features: [
      t("products.aiAnnotation.features.imageAnnotation"),
      t("products.aiAnnotation.features.textAnnotation"),
      t("products.aiAnnotation.features.audioAnnotation"),
      t("products.aiAnnotation.features.qualityControl")
    ],
    techSpecs: {
      deployment: "云端部署",
      security: "企业级安全",
      availability: "99.9%可用性",
      support: "24/7技术支持"
    }
  },
  "cpu-rental": {
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    features: [
      t("products.cpuRental.features.highPerformance"),
      t("products.cpuRental.features.elasticScaling"),
      t("products.cpuRental.features.payAsYouGo"),
      t("products.cpuRental.features.monitoring247")
    ],
    techSpecs: {
      deployment: "多地区部署",
      security: "数据加密",
      availability: "99.9%可用性",
      support: "专业技术支持"
    }
  },
  "education-management": {
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    features: [
      t("products.educationManagement.features.courseManagement"),
      t("products.educationManagement.features.onlineExam"),
      t("products.educationManagement.features.studentTracking"),
      t("products.educationManagement.features.certificateSystem")
    ],
    techSpecs: {
      deployment: "私有云/公有云",
      security: "数据安全保护",
      availability: "高可用架构",
      support: "专业培训支持"
    }
  }
});

// Legacy exports for backward compatibility
export const products = [];
export const productDetails = {};
